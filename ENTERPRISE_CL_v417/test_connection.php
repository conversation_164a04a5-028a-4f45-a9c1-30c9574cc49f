<?php
/**
 * Script de prueba para verificar la conexión con MySQL Aurora 8.0
 * Incluye pruebas de charset utf8mb4 y funcionalidad básica
 */

// Incluir la configuración y conexión
include('config.php');
include('dbConn.php');

echo "<h2>Prueba de Conexión MySQL Aurora 8.0</h2>";

// Verificar que la conexión PDO existe
if (!isset($dbh) || !$dbh instanceof PDO) {
    die("<p style='color: red;'>ERROR: Conexión PDO no disponible</p>");
}

echo "<p style='color: green;'>✓ Conexión PDO establecida correctamente</p>";

try {
    // Probar información del servidor
    $stmt = $dbh->query("SELECT VERSION() as version, @@character_set_database as charset, @@collation_database as collation");
    $info = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<h3>Información del Servidor:</h3>";
    echo "<ul>";
    echo "<li><strong>Versión MySQL:</strong> " . $info['version'] . "</li>";
    echo "<li><strong>Charset de BD:</strong> " . $info['charset'] . "</li>";
    echo "<li><strong>Collation de BD:</strong> " . $info['collation'] . "</li>";
    echo "</ul>";
    
    // Probar charset utf8mb4
    $stmt = $dbh->query("SHOW VARIABLES LIKE 'character_set%'");
    $charsets = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Configuración de Charset:</h3>";
    echo "<ul>";
    foreach ($charsets as $charset) {
        $color = (strpos($charset['Value'], 'utf8mb4') !== false) ? 'green' : 'orange';
        echo "<li style='color: $color;'><strong>" . $charset['Variable_name'] . ":</strong> " . $charset['Value'] . "</li>";
    }
    echo "</ul>";
    
    // Probar una consulta simple
    $stmt = $dbh->query("SELECT 1 as test, 'Prueba con emojis: 😀🚀💻' as emoji_test");
    $test = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<h3>Prueba de Consulta:</h3>";
    echo "<p style='color: green;'>✓ Consulta básica: " . $test['test'] . "</p>";
    echo "<p style='color: green;'>✓ Prueba UTF8MB4: " . $test['emoji_test'] . "</p>";
    
    // Probar conexión a tablas existentes (si existen)
    $tables_to_check = ['account', 'company', 'trafficMT'];
    echo "<h3>Verificación de Tablas:</h3>";
    echo "<ul>";
    
    foreach ($tables_to_check as $table) {
        try {
            $stmt = $dbh->query("SELECT COUNT(*) as count FROM $table LIMIT 1");
            $count = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "<li style='color: green;'>✓ Tabla '$table' accesible (registros: " . $count['count'] . ")</li>";
        } catch (PDOException $e) {
            echo "<li style='color: orange;'>⚠ Tabla '$table' no accesible: " . $e->getMessage() . "</li>";
        }
    }
    echo "</ul>";
    
    // Probar funciones auxiliares PDO
    include_once('includes/php/pdo_helpers.php');
    
    echo "<h3>Prueba de Funciones Auxiliares:</h3>";
    try {
        $connection_test = getPDOConnection();
        echo "<p style='color: green;'>✓ getPDOConnection() funciona correctamente</p>";
        
        $test_query = "SELECT 'Helper test' as message";
        $result = pdoFetchArray($test_query);
        echo "<p style='color: green;'>✓ pdoFetchArray() funciona: " . $result['message'] . "</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Error en funciones auxiliares: " . $e->getMessage() . "</p>";
    }
    
    echo "<h3>Configuración de Conexión:</h3>";
    echo "<ul>";
    echo "<li><strong>Host:</strong> " . SERVERDB . "</li>";
    echo "<li><strong>Base de datos:</strong> " . DB . "</li>";
    echo "<li><strong>Usuario:</strong> " . USERDB . "</li>";
    echo "<li><strong>Charset configurado:</strong> utf8mb4</li>";
    echo "</ul>";
    
    echo "<h3>Recomendaciones:</h3>";
    echo "<ul>";
    echo "<li>✓ Usar prepared statements para todas las consultas</li>";
    echo "<li>✓ Migrar todas las funciones mysql_* a PDO</li>";
    echo "<li>✓ Verificar que todas las tablas usen charset utf8mb4</li>";
    echo "<li>✓ Actualizar el código que usa mysql_query, mysql_fetch_array, etc.</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>ERROR: " . $e->getMessage() . "</p>";
    echo "<p>Código de error: " . $e->getCode() . "</p>";
    
    // Información adicional para debugging
    echo "<h3>Información de Debug:</h3>";
    echo "<ul>";
    echo "<li><strong>Host configurado:</strong> " . SERVERDB . "</li>";
    echo "<li><strong>Base de datos:</strong> " . DB . "</li>";
    echo "<li><strong>Usuario:</strong> " . USERDB . "</li>";
    echo "</ul>";
    
    echo "<h3>Posibles soluciones:</h3>";
    echo "<ul>";
    echo "<li>Verificar que el host y puerto sean correctos</li>";
    echo "<li>Verificar credenciales de acceso</li>";
    echo "<li>Verificar que el security group permita conexiones desde esta IP</li>";
    echo "<li>Verificar que MySQL Aurora esté ejecutándose</li>";
    echo "</ul>";
}

echo "<hr>";
echo "<p><em>Prueba completada - " . date('Y-m-d H:i:s') . "</em></p>";
?>
