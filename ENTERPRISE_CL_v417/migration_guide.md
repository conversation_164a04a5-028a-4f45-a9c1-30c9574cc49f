# Guía de Migración MySQL Aurora 8.0 + UTF8MB4

## Problema Identificado

Tu aplicación estaba usando funciones MySQL obsoletas que fueron eliminadas en PHP 7.0:
- `mysql_connect()`
- `mysql_query()`
- `mysql_fetch_array()`
- `mysql_num_rows()`
- `mysql_insert_id()`
- `mysql_affected_rows()`

Además, estaba configurada para `utf8` en lugar de `utf8mb4`, lo cual puede causar problemas con caracteres especiales y emojis.

## Cambios Realizados

### 1. Actualización de dbConn.php
- ✅ Migrado de `mysql_connect()` a PDO
- ✅ Configurado charset `utf8mb4` con collation `utf8mb4_unicode_ci`
- ✅ Agregado manejo de errores robusto
- ✅ Configuración específica para MySQL Aurora 8.0

### 2. Funciones Auxiliares PDO
- ✅ Creado `includes/php/pdo_helpers.php` con funciones de compatibilidad
- ✅ Funciones para migración gradual disponibles

### 3. Funciones Actualizadas en general_funcs.php
- ✅ `validacion_quota_sms()` - Migrada a PDO con prepared statements
- ✅ `insertTempFile()` - Migrada a PDO
- ✅ `updateStatusProcess()` - Migrada a PDO
- ✅ `updateProcessFile()` - Migrada a PDO
- ✅ `updateProcessedFile()` - Migrada a PDO
- ✅ `get_count_row_query()` - Migrada a PDO

## Archivos que AÚN Necesitan Migración

### Archivos Críticos:
1. `includes/php/general_funcs.php` - Funciones restantes
2. `includes/php/sections_report_gesReportFile_old.php`
3. `includes/php/sections_report_gesReportMO_old.php`
4. `includes/php/sections_provisioning.php`
5. `includes/php/general_function_return.php`
6. `ajaxfuncs/campaign/resendCampaign.php`
7. `ajaxfuncs/campaign/listPendingCampaign.php`

## Pasos para Completar la Migración

### Paso 1: Probar la Conexión
```bash
# Acceder a test_connection.php desde el navegador
http://tu-servidor/test_connection.php
```

### Paso 2: Migración Gradual
Para cada archivo que use funciones MySQL obsoletas:

1. **Reemplazar mysql_query():**
```php
// Antes:
$sql = mysql_query("SELECT * FROM tabla WHERE id = $id");

// Después:
$stmt = $dbh->prepare("SELECT * FROM tabla WHERE id = ?");
$stmt->execute([$id]);
```

2. **Reemplazar mysql_fetch_array():**
```php
// Antes:
$row = mysql_fetch_array($sql);

// Después:
$row = $stmt->fetch(PDO::FETCH_ASSOC);
```

3. **Reemplazar mysql_num_rows():**
```php
// Antes:
$count = mysql_num_rows($sql);

// Después:
$count = $stmt->rowCount();
```

### Paso 3: Verificar Charset en Base de Datos
```sql
-- Verificar charset de las tablas
SELECT TABLE_NAME, TABLE_COLLATION 
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'mcs_enterprise_cl';

-- Si es necesario, convertir tablas a utf8mb4:
ALTER TABLE nombre_tabla CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

## Configuración Recomendada para MySQL Aurora

### Variables de Configuración:
```sql
SET character_set_server = utf8mb4;
SET collation_server = utf8mb4_unicode_ci;
SET character_set_database = utf8mb4;
SET collation_database = utf8mb4_unicode_ci;
```

### Parámetros de Conexión PDO:
```php
$options = [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false,
    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
];
```

## Errores Comunes y Soluciones

### Error: "Access denied"
- Verificar credenciales en `config.php`
- Verificar que el usuario tenga permisos en Aurora
- Verificar security groups de AWS

### Error: "Connection refused"
- Verificar que el endpoint de Aurora sea correcto
- Verificar que el puerto 3306 esté abierto
- Verificar conectividad de red

### Error: "Character set"
- Verificar que las tablas usen utf8mb4
- Verificar configuración de charset en Aurora
- Verificar parámetros de conexión PDO

## Próximos Pasos

1. **Ejecutar test_connection.php** para verificar la conexión
2. **Migrar archivos uno por uno** usando las funciones auxiliares
3. **Probar funcionalidad** después de cada migración
4. **Actualizar charset de tablas** si es necesario
5. **Eliminar funciones de compatibilidad** una vez completada la migración

## Funciones de Compatibilidad Temporales

Mientras migras, puedes usar estas funciones de compatibilidad:
- `mysql_query_compat()`
- `mysql_fetch_array_compat()`
- `mysql_num_rows_compat()`
- `mysql_insert_id_compat()`

Estas están disponibles en `includes/php/pdo_helpers.php`.
