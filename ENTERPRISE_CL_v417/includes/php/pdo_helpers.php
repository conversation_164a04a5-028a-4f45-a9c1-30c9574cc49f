<?php
/**
 * Funciones auxiliares para PDO - Migración desde MySQL obsoleto
 * Compatible con MySQL Aurora 8.0 y charset utf8mb4
 */

/**
 * Obtiene la conexión PDO global
 * @return PDO
 */
function getPDOConnection() {
    global $dbh;
    if (!$dbh) {
        throw new Exception("Conexión PDO no disponible");
    }
    return $dbh;
}

/**
 * Ejecuta una consulta SELECT y retorna el primer resultado
 * @param string $query
 * @param array $params
 * @return array|false
 */
function pdoFetchArray($query, $params = []) {
    $dbh = getPDOConnection();
    $stmt = $dbh->prepare($query);
    $stmt->execute($params);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * Ejecuta una consulta SELECT y retorna todos los resultados
 * @param string $query
 * @param array $params
 * @return array
 */
function pdoFetchAll($query, $params = []) {
    $dbh = getPDOConnection();
    $stmt = $dbh->prepare($query);
    $stmt->execute($params);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Ejecuta una consulta INSERT/UPDATE/DELETE
 * @param string $query
 * @param array $params
 * @return bool
 */
function pdoQuery($query, $params = []) {
    $dbh = getPDOConnection();
    $stmt = $dbh->prepare($query);
    return $stmt->execute($params);
}

/**
 * Obtiene el número de filas afectadas por la última consulta
 * @param string $query
 * @param array $params
 * @return int
 */
function pdoNumRows($query, $params = []) {
    $dbh = getPDOConnection();
    $stmt = $dbh->prepare($query);
    $stmt->execute($params);
    return $stmt->rowCount();
}

/**
 * Obtiene el último ID insertado
 * @return string
 */
function pdoInsertId() {
    $dbh = getPDOConnection();
    return $dbh->lastInsertId();
}

/**
 * Funciones de compatibilidad para migración gradual
 */

/**
 * Reemplazo para mysql_query
 * @param string $query
 * @return PDOStatement|false
 */
function mysql_query_compat($query) {
    try {
        $dbh = getPDOConnection();
        return $dbh->query($query);
    } catch (Exception $e) {
        error_log("Error en mysql_query_compat: " . $e->getMessage());
        return false;
    }
}

/**
 * Reemplazo para mysql_fetch_array
 * @param PDOStatement $result
 * @return array|false
 */
function mysql_fetch_array_compat($result) {
    if ($result instanceof PDOStatement) {
        return $result->fetch(PDO::FETCH_ASSOC);
    }
    return false;
}

/**
 * Reemplazo para mysql_num_rows
 * @param PDOStatement $result
 * @return int
 */
function mysql_num_rows_compat($result) {
    if ($result instanceof PDOStatement) {
        return $result->rowCount();
    }
    return 0;
}

/**
 * Reemplazo para mysql_insert_id
 * @return string
 */
function mysql_insert_id_compat() {
    return pdoInsertId();
}

/**
 * Reemplazo para mysql_affected_rows
 * @param PDOStatement $result
 * @return int
 */
function mysql_affected_rows_compat($result = null) {
    if ($result instanceof PDOStatement) {
        return $result->rowCount();
    }
    return 0;
}

/**
 * Escapa cadenas para prevenir inyección SQL (usar prepared statements es mejor)
 * @param string $string
 * @return string
 */
function mysql_real_escape_string_compat($string) {
    $dbh = getPDOConnection();
    return trim($dbh->quote($string), "'");
}

?>
