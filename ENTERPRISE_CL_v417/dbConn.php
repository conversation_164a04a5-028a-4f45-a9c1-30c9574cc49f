<?php
	include('config.php');

	// Configuración para MySQL Aurora 8.0 con utf8mb4
	try {
		// Opciones PDO para MySQL Aurora 8.0
		$options = [
			PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
			PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
			PDO::ATTR_EMULATE_PREPARES => false,
			PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
		];

		// Construir DSN para MySQL Aurora
		$dsn = "mysql:host=" . SERVERDB . ";dbname=" . DB . ";charset=utf8mb4";

		// Crear conexión PDO
		$dbh = new PDO($dsn, USERDB, PASSDB, $options);

		// Configurar timezone si es necesario
		$dbh->exec("SET time_zone = '+00:00'");

	} catch (PDOException $e) {
		// Log del error para debugging
		error_log("Error de conexión a la base de datos: " . $e->getMessage());
		die("Error de conexión a la base de datos. Por favor, contacte al administrador.");
	}

	if (!isset($_SESSION)) { session_start(); }

	// Incluir funciones auxiliares para PDO
	include_once('includes/php/pdo_helpers.php');
?>
