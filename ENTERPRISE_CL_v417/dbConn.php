<?php
	// Incluir configuración
	include('config.php');

	// Separar host y puerto si están juntos en SERVERDB
	$host_parts = explode(':', SERVERDB);
	$host = $host_parts[0];
	$port = isset($host_parts[1]) ? $host_parts[1] : '3306';

	// Intentar conexión sin el puerto en la cadena (recomendado en PHP 5.6)
	$connection = @mysql_connect($host, USERDB, PASSDB);
	if (!$connection) {
		die("Error de conexión a la base de datos: " . mysql_error());
	}

	// Establecer charset y collation compatibles con PHP 5.6 y RDS
	mysql_query("SET character_set_results = 'utf8'", $connection);
	mysql_query("SET names 'utf8'", $connection);
	mysql_query("SET character_set_client = 'utf8'", $connection);
	mysql_query("SET character_set_connection = 'utf8'", $connection);
	mysql_query("SET collation_connection = 'utf8_general_ci'", $connection);

	// Seleccionar la base de datos
	if (!mysql_select_db(DB, $connection)) {
		die("Error seleccionando base de datos: " . mysql_error());
	}

	// Iniciar sesión si aún no ha iniciado
	if (!isset($_SESSION)) {
		session_start();
	}

	// Puedes usar $connection en el resto del sistema
?>
